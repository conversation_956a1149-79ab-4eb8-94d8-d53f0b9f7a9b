#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Coser分析工作流 Web应用
基于Flask的前端界面
"""

import os
import json
import uuid
import threading
import time
from datetime import datetime
from flask import Flask, render_template, request, jsonify, send_from_directory
from werkzeug.utils import secure_filename
from coser_analysis_flow import run_coser_analysis
from database import db

# 创建Flask应用
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# 全局进度跟踪字典
analysis_progress = {}

# 配置上传目录
UPLOAD_FOLDER = 'static/uploads'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}

# 确保上传目录存在
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    """主页面"""
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    """处理文件上传"""
    try:
        print(f"📤 收到上传请求")
        print(f"📋 请求文件: {list(request.files.keys())}")

        if 'file' not in request.files:
            print("❌ 错误: 没有找到文件字段")
            return jsonify({'error': '没有选择文件'}), 400

        file = request.files['file']
        print(f"📁 文件名: {file.filename}")
        print(f"📊 文件类型: {file.content_type}")

        if file.filename == '':
            print("❌ 错误: 文件名为空")
            return jsonify({'error': '没有选择文件'}), 400

        if file and allowed_file(file.filename):
            # 获取文件扩展名
            file_ext = os.path.splitext(file.filename)[1].lower()

            # 生成安全的文件名（使用UUID避免中文文件名问题）
            unique_filename = f"{uuid.uuid4().hex}{file_ext}"
            filepath = os.path.join(UPLOAD_FOLDER, unique_filename)

            print(f"💾 保存文件到: {filepath}")
            print(f"🔤 原始文件名: {file.filename}")
            print(f"🆔 生成文件名: {unique_filename}")

            # 保存文件
            file.save(filepath)

            print(f"✅ 文件上传成功: {unique_filename}")

            return jsonify({
                'success': True,
                'filename': unique_filename,
                'filepath': filepath
            })
        else:
            print(f"❌ 错误: 不支持的文件格式 - {file.filename}")
            return jsonify({'error': '不支持的文件格式'}), 400

    except Exception as e:
        print(f"💥 上传异常: {str(e)}")
        return jsonify({'error': f'上传失败: {str(e)}'}), 500

def run_analysis_with_progress(task_id, filepath, character_name, additional_description):
    """带进度跟踪的分析函数"""
    start_time = time.time()

    try:
        # 初始化进度
        analysis_progress[task_id] = {
            'status': 'running',
            'step': 1,
            'step_name': '图片分析中...',
            'progress': 10,
            'result': None,
            'error': None
        }

        # 获取文件信息
        file_size = os.path.getsize(filepath) if os.path.exists(filepath) else 0
        image_filename = os.path.basename(filepath)

        # 创建自定义的分析流程，支持进度回调
        from coser_analysis_flow import CoserAnalysisFlow

        # 创建工作流实例
        flow = CoserAnalysisFlow()
        flow.state.image_path = filepath
        flow.state.character_name = character_name
        flow.state.additional_description = additional_description

        # 步骤1：图片分析
        analysis_progress[task_id].update({
            'step': 1,
            'step_name': '正在进行图片分析...',
            'progress': 30
        })

        # 执行图片分析
        flow.start_analysis()
        image_result = flow.image_analysis_step("开始")

        # 步骤2：综合分析
        analysis_progress[task_id].update({
            'step': 2,
            'step_name': '正在生成综合分析...',
            'progress': 70
        })

        # 执行综合分析
        comprehensive_result = flow.comprehensive_analysis_step(image_result)

        # 计算分析耗时
        analysis_duration = time.time() - start_time

        # 保存到数据库
        analysis_progress[task_id].update({
            'step': 3,
            'step_name': '保存分析结果...',
            'progress': 90
        })

        try:
            record_id = db.save_analysis_record(
                character_name=character_name,
                additional_description=additional_description,
                image_filename=image_filename,
                image_path=filepath,
                image_analysis=flow.state.image_analysis,
                comprehensive_analysis=flow.state.comprehensive_analysis,
                file_size=file_size,
                analysis_duration=analysis_duration
            )
            print(f"✅ 分析结果已保存到数据库: {record_id}")
        except Exception as db_error:
            print(f"⚠️ 保存到数据库失败: {str(db_error)}")

        # 完成
        result_data = {
            'timestamp': datetime.now().isoformat(),
            'character_name': character_name,
            'additional_description': additional_description,
            'image_analysis': flow.state.image_analysis,
            'comprehensive_analysis': flow.state.comprehensive_analysis,
            'analysis_duration': analysis_duration,
            'file_size': file_size
        }

        analysis_progress[task_id].update({
            'status': 'completed',
            'step': 4,
            'step_name': '分析完成！',
            'progress': 100,
            'result': result_data
        })

    except Exception as e:
        analysis_progress[task_id].update({
            'status': 'error',
            'error': str(e)
        })

@app.route('/analyze', methods=['POST'])
def analyze_image():
    """分析图片"""
    try:
        data = request.get_json()

        # 获取参数
        filepath = data.get('filepath')
        character_name = data.get('character_name', '')
        additional_description = data.get('additional_description', '')

        if not filepath or not os.path.exists(filepath):
            return jsonify({'error': '图片文件不存在'}), 400

        if not character_name.strip():
            return jsonify({'error': '请输入角色名称'}), 400

        # 生成任务ID
        task_id = str(uuid.uuid4())

        # 启动后台分析任务
        thread = threading.Thread(
            target=run_analysis_with_progress,
            args=(task_id, filepath, character_name.strip(), additional_description.strip())
        )
        thread.daemon = True
        thread.start()

        return jsonify({
            'success': True,
            'task_id': task_id
        })

    except Exception as e:
        return jsonify({'error': f'分析失败: {str(e)}'}), 500

@app.route('/progress/<task_id>', methods=['GET'])
def get_progress(task_id):
    """获取分析进度"""
    if task_id not in analysis_progress:
        return jsonify({'error': '任务不存在'}), 404

    progress_data = analysis_progress[task_id]

    # 如果任务完成或出错，可以清理进度数据（可选）
    if progress_data['status'] in ['completed', 'error']:
        # 保留一段时间后再清理，这里暂时不清理
        pass

    return jsonify(progress_data)

@app.route('/download_result', methods=['POST'])
def download_result():
    """下载分析结果"""
    try:
        data = request.get_json()
        result_data = data.get('result')
        
        if not result_data:
            return jsonify({'error': '没有分析结果'}), 400
        
        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        character_name = result_data.get('character_name', 'unknown')
        filename = f"coser_analysis_{character_name}_{timestamp}.txt"
        
        # 格式化内容
        content = f"""Coser分析结果
================

分析时间: {result_data.get('timestamp', '')}
角色名称: {result_data.get('character_name', '')}
补充描述: {result_data.get('additional_description', '')}

图片分析结果:
{'-' * 50}
{result_data.get('image_analysis', '')}

综合分析结果:
{'-' * 50}
{result_data.get('comprehensive_analysis', '')}
"""
        
        # 保存到临时文件
        temp_filepath = os.path.join(UPLOAD_FOLDER, filename)
        with open(temp_filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return jsonify({
            'success': True,
            'download_url': f'/download/{filename}'
        })
        
    except Exception as e:
        return jsonify({'error': f'生成下载文件失败: {str(e)}'}), 500

@app.route('/download/<filename>')
def download_file(filename):
    """下载文件"""
    try:
        return send_from_directory(UPLOAD_FOLDER, filename, as_attachment=True)
    except Exception as e:
        return jsonify({'error': f'下载失败: {str(e)}'}), 500

@app.route('/api/history', methods=['GET'])
def get_history():
    """获取分析历史记录列表"""
    try:
        # 获取查询参数
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 20))
        character_name = request.args.get('character_name', '').strip()
        status = request.args.get('status', '').strip()

        # 计算偏移量
        offset = (page - 1) * limit

        # 查询历史记录
        records = db.get_analysis_history(
            limit=limit,
            offset=offset,
            character_name=character_name if character_name else None,
            status=status if status else None
        )

        # 获取搜索结果总数（用于分页）
        total_records = db.get_analysis_history(
            limit=999999,  # 获取所有匹配记录来计算总数
            offset=0,
            character_name=character_name if character_name else None,
            status=status if status else None
        )

        # 获取统计信息
        stats = db.get_statistics()

        return jsonify({
            'success': True,
            'data': records,
            'pagination': {
                'page': page,
                'limit': limit,
                'total': len(total_records)
            },
            'statistics': stats
        })

    except Exception as e:
        return jsonify({'error': f'获取历史记录失败: {str(e)}'}), 500

@app.route('/api/history/<record_id>', methods=['GET'])
def get_history_detail(record_id):
    """获取分析记录详情"""
    try:
        record = db.get_analysis_detail(record_id)

        if not record:
            return jsonify({'error': '记录不存在'}), 404

        return jsonify({
            'success': True,
            'data': record
        })

    except Exception as e:
        return jsonify({'error': f'获取记录详情失败: {str(e)}'}), 500

@app.route('/api/history/<record_id>', methods=['DELETE'])
def delete_history_record(record_id):
    """删除分析记录"""
    try:
        success = db.delete_analysis_record(record_id)

        if not success:
            return jsonify({'error': '记录不存在或删除失败'}), 404

        return jsonify({
            'success': True,
            'message': '记录删除成功'
        })

    except Exception as e:
        return jsonify({'error': f'删除记录失败: {str(e)}'}), 500

@app.route('/api/statistics', methods=['GET'])
def get_statistics():
    """获取统计信息"""
    try:
        stats = db.get_statistics()

        return jsonify({
            'success': True,
            'data': stats
        })

    except Exception as e:
        return jsonify({'error': f'获取统计信息失败: {str(e)}'}), 500

@app.errorhandler(413)
def too_large(e):
    """文件过大错误处理"""
    return jsonify({'error': '文件大小超过限制（最大16MB）'}), 413

@app.errorhandler(404)
def not_found(e):
    """404错误处理"""
    return jsonify({'error': '页面不存在'}), 404

@app.errorhandler(500)
def internal_error(e):
    """500错误处理"""
    return jsonify({'error': '服务器内部错误'}), 500

if __name__ == '__main__':
    print("🚀 启动Coser分析工作流Web应用")
    print("📱 访问地址: http://localhost:8888")
    print("🎭 开始您的Coser分析之旅！")

    # 开发模式运行
    app.run(
        host='0.0.0.0',
        port=8888,
        debug=True,
        threaded=True
    )
