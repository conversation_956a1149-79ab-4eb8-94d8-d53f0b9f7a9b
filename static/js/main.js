// 全局变量
let uploadedFilePath = null;
let analysisResult = null;
let isUploading = false; // 防止重复上传的标志
let lastClickTime = 0; // 上次点击时间戳
let fileDialogOpen = false; // 文件对话框是否打开

// 历史记录相关变量
let currentPage = 1;
let currentHistoryRecord = null;
let searchQuery = '';

// DOM元素
const uploadArea = document.getElementById('uploadArea');
const fileInput = document.getElementById('fileInput');
const imagePreview = document.getElementById('imagePreview');
const previewImg = document.getElementById('previewImg');
const characterNameInput = document.getElementById('characterName');
const additionalDescriptionInput = document.getElementById('additionalDescription');
const analyzeBtn = document.getElementById('analyzeBtn');
const loadingSection = document.getElementById('loadingSection');
const resultsSection = document.getElementById('resultsSection');

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    initializeHistoryFeatures();
});

/**
 * 初始化事件监听器
 */
function initializeEventListeners() {
    console.log('初始化事件监听器');

    // 移除可能存在的旧监听器（防止重复绑定）
    fileInput.removeEventListener('change', handleFileSelect);
    uploadArea.removeEventListener('click', handleUploadAreaClick);

    // 文件输入变化
    fileInput.addEventListener('change', handleFileSelect);

    // 拖拽上传
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleDrop);
    uploadArea.addEventListener('click', handleUploadAreaClick);

    // 表单输入变化
    characterNameInput.addEventListener('input', validateForm);
    additionalDescriptionInput.addEventListener('input', validateForm);

    console.log('事件监听器初始化完成');
}

/**
 * 处理上传区域点击
 */
function handleUploadAreaClick(event) {
    const currentTime = Date.now();
    console.log('上传区域点击事件触发, 时间:', currentTime);

    // 防抖检查 - 500ms内的重复点击将被忽略
    if (currentTime - lastClickTime < 500) {
        console.log('点击过于频繁，忽略 (间隔:', currentTime - lastClickTime, 'ms)');
        return;
    }

    // 防止重复处理
    if (isUploading || fileDialogOpen) {
        console.log('正在上传中或文件对话框已打开，忽略点击');
        return;
    }

    lastClickTime = currentTime;
    fileDialogOpen = true;

    console.log('打开文件选择对话框');
    fileInput.click();

    // 设置超时重置对话框状态（防止对话框被取消时状态卡住）
    setTimeout(() => {
        if (fileDialogOpen) {
            console.log('重置文件对话框状态（超时）');
            fileDialogOpen = false;
        }
    }, 3000);
}

/**
 * 处理文件选择
 */
function handleFileSelect(event) {
    console.log('文件选择事件触发');

    // 重置对话框状态
    fileDialogOpen = false;

    // 防止重复处理
    if (isUploading) {
        console.log('正在上传中，忽略重复选择');
        return;
    }

    const file = event.target.files[0];
    if (file) {
        console.log('选择的文件:', file.name, file.size, file.type);
        uploadFile(file);
    } else {
        console.log('没有选择文件或取消选择');
    }
}

/**
 * 处理拖拽悬停
 */
function handleDragOver(event) {
    event.preventDefault();
    uploadArea.classList.add('dragover');
}

/**
 * 处理拖拽离开
 */
function handleDragLeave(event) {
    event.preventDefault();
    uploadArea.classList.remove('dragover');
}

/**
 * 处理文件拖拽放下
 */
function handleDrop(event) {
    event.preventDefault();
    uploadArea.classList.remove('dragover');
    
    const files = event.dataTransfer.files;
    if (files.length > 0) {
        uploadFile(files[0]);
    }
}

/**
 * 上传文件
 */
async function uploadFile(file) {
    console.log('开始上传文件:', file.name);

    // 设置上传状态
    isUploading = true;

    // 验证文件类型
    const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
        console.error('不支持的文件类型:', file.type);
        showToast('不支持的文件格式，请选择图片文件', 'error');
        safeResetFileInput();
        return;
    }

    // 验证文件大小 (16MB)
    if (file.size > 16 * 1024 * 1024) {
        console.error('文件太大:', file.size);
        showToast('文件大小超过限制（最大16MB）', 'error');
        safeResetFileInput();
        return;
    }
    
    const formData = new FormData();
    formData.append('file', file);
    
    try {
        showToast('正在上传文件...', 'info');
        
        const response = await fetch('/upload', {
            method: 'POST',
            body: formData
        });

        console.log('上传响应状态:', response.status);

        if (!response.ok) {
            throw new Error(`HTTP错误: ${response.status}`);
        }

        const result = await response.json();
        console.log('上传响应数据:', result);

        if (result.success) {
            console.log('上传成功，文件路径:', result.filepath);
            uploadedFilePath = result.filepath;
            showImagePreview(file);
            showToast('文件上传成功', 'success');
            validateForm();
        } else {
            console.error('上传失败:', result.error);
            showToast(result.error || '上传失败', 'error');
            safeResetFileInput();
        }
    } catch (error) {
        console.error('上传错误:', error);
        showToast('上传失败: ' + error.message, 'error');
        safeResetFileInput();
    } finally {
        // 重置上传状态
        isUploading = false;
        fileDialogOpen = false;
    }
}

/**
 * 显示图片预览
 */
function showImagePreview(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        previewImg.src = e.target.result;
        uploadArea.style.display = 'none';
        imagePreview.style.display = 'block';
    };
    reader.readAsDataURL(file);
}

/**
 * 重置文件输入
 */
function resetFileInput() {
    console.log('重置文件输入');
    fileInput.value = '';
}

/**
 * 安全地重置文件输入（避免重复触发）
 */
function safeResetFileInput() {
    console.log('安全重置文件输入');
    // 延迟重置，避免立即触发事件
    setTimeout(() => {
        fileInput.value = '';
        isUploading = false;
    }, 100);
}

/**
 * 移除图片
 */
function removeImage() {
    console.log('移除图片');
    uploadedFilePath = null;
    uploadArea.style.display = 'block';
    imagePreview.style.display = 'none';
    resetFileInput();
    validateForm();
}

/**
 * 验证表单
 */
function validateForm() {
    const hasFile = uploadedFilePath !== null;
    const hasCharacterName = characterNameInput.value.trim() !== '';
    
    analyzeBtn.disabled = !(hasFile && hasCharacterName);
}

/**
 * 开始分析
 */
async function startAnalysis() {
    if (!uploadedFilePath || !characterNameInput.value.trim()) {
        showToast('请上传图片并输入角色名称', 'error');
        return;
    }

    // 清空之前的分析结果
    analysisResult = null;

    // 隐藏结果区域，显示加载状态
    resultsSection.style.display = 'none';
    loadingSection.style.display = 'block';

    // 重置进度步骤
    resetProgressSteps();

    try {
        // 启动分析任务
        const requestData = {
            filepath: uploadedFilePath,
            character_name: characterNameInput.value.trim(),
            additional_description: additionalDescriptionInput.value.trim()
        };

        const response = await fetch('/analyze', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });

        const result = await response.json();

        if (result.success && result.task_id) {
            // 开始轮询进度
            pollProgress(result.task_id);
        } else {
            throw new Error(result.error || '启动分析失败');
        }

    } catch (error) {
        loadingSection.style.display = 'none';
        showToast('分析失败: ' + error.message, 'error');
    }
}

/**
 * 轮询分析进度
 */
async function pollProgress(taskId) {
    console.log('开始轮询进度，任务ID:', taskId);

    const pollInterval = setInterval(async () => {
        try {
            const response = await fetch(`/progress/${taskId}`);
            const progressData = await response.json();

            console.log('进度更新:', progressData);

            if (progressData.error) {
                clearInterval(pollInterval);
                loadingSection.style.display = 'none';
                showToast('获取进度失败: ' + progressData.error, 'error');
                return;
            }

            // 更新进度显示
            updateProgress(progressData);

            // 检查是否完成
            if (progressData.status === 'completed') {
                clearInterval(pollInterval);
                if (progressData.result) {
                    analysisResult = progressData.result;
                    setTimeout(() => {
                        showResults(progressData.result);
                    }, 1000);
                } else {
                    loadingSection.style.display = 'none';
                    showToast('分析完成但未获取到结果', 'error');
                }
            } else if (progressData.status === 'error') {
                clearInterval(pollInterval);
                loadingSection.style.display = 'none';
                showToast('分析失败: ' + (progressData.error || '未知错误'), 'error');
            }

        } catch (error) {
            console.error('轮询进度错误:', error);
            clearInterval(pollInterval);
            loadingSection.style.display = 'none';
            showToast('获取进度失败: ' + error.message, 'error');
        }
    }, 1000); // 每秒轮询一次
}

/**
 * 更新进度显示
 */
function updateProgress(progressData) {
    // 更新步骤状态
    if (progressData.step) {
        setActiveStep(progressData.step);
    }

    // 更新加载文本
    if (progressData.step_name) {
        updateLoadingText(progressData.step_name);
    }

    console.log(`进度: ${progressData.progress || 0}% - ${progressData.step_name || '处理中...'}`);
}

/**
 * 重置进度步骤
 */
function resetProgressSteps() {
    document.querySelectorAll('.step').forEach(step => {
        step.classList.remove('active');
    });
}

/**
 * 设置当前活动步骤
 */
function setActiveStep(stepNumber) {
    document.getElementById(`step${stepNumber}`).classList.add('active');
}

/**
 * 更新加载文本
 */
function updateLoadingText(text) {
    document.getElementById('loadingText').textContent = text;
}

/**
 * 显示分析结果
 */
function showResults(result) {
    // 隐藏加载状态，显示结果
    loadingSection.style.display = 'none';
    resultsSection.style.display = 'block';
    
    // 填充结果内容
    document.getElementById('imageAnalysisResult').textContent = result.image_analysis;
    document.getElementById('comprehensiveAnalysisResult').textContent = result.comprehensive_analysis;
    
    // 滚动到结果区域
    resultsSection.scrollIntoView({ behavior: 'smooth' });
    
    showToast('分析完成！', 'success');
}

/**
 * 下载结果
 */
async function downloadResult() {
    if (!analysisResult) {
        showToast('没有可下载的结果', 'error');
        return;
    }

    downloadResultData(analysisResult);
}

/**
 * 新的分析
 */
function newAnalysis() {
    // 重置所有状态
    uploadedFilePath = null;
    analysisResult = null;
    
    // 重置界面
    removeImage();
    characterNameInput.value = '';
    additionalDescriptionInput.value = '';
    resultsSection.style.display = 'none';
    loadingSection.style.display = 'none';
    
    // 滚动到顶部
    window.scrollTo({ top: 0, behavior: 'smooth' });
    
    showToast('已重置，可以开始新的分析', 'info');
}

/**
 * 显示消息提示
 */
function showToast(message, type = 'info') {
    const toast = document.getElementById('toast');
    toast.textContent = message;
    toast.className = `toast ${type}`;
    toast.classList.add('show');

    setTimeout(() => {
        toast.classList.remove('show');
    }, 3000);
}

// ==================== 标签页切换功能 ====================

/**
 * 切换标签页
 */
function switchTab(tabName) {
    // 移除所有标签的active类
    document.querySelectorAll('.nav-tab').forEach(tab => {
        tab.classList.remove('active');
    });

    // 隐藏所有标签页内容
    document.querySelectorAll('.tab-content').forEach(content => {
        content.style.display = 'none';
    });

    // 激活当前标签
    event.target.classList.add('active');

    // 显示对应的标签页内容
    const targetTab = document.getElementById(tabName + 'Tab');
    if (targetTab) {
        targetTab.style.display = 'block';
    }

    // 根据标签页加载相应数据
    if (tabName === 'history') {
        loadHistory();
    } else if (tabName === 'statistics') {
        loadStatistics();
    }
}

// ==================== 历史记录功能 ====================

/**
 * 加载历史记录
 */
async function loadHistory(page = 1) {
    try {
        currentPage = page;
        const historyList = document.getElementById('historyList');

        // 显示加载状态
        historyList.innerHTML = `
            <div class="loading-placeholder">
                <i class="fas fa-spinner fa-spin"></i> 加载中...
            </div>
        `;

        // 构建查询参数
        const params = new URLSearchParams({
            page: page,
            limit: 10
        });

        if (searchQuery) {
            params.append('character_name', searchQuery);
        }

        // 请求历史记录
        const response = await fetch(`/api/history?${params}`);
        const data = await response.json();

        if (data.success) {
            displayHistoryList(data.data);
            updatePagination(data.pagination);
        } else {
            throw new Error(data.error || '加载失败');
        }

    } catch (error) {
        console.error('加载历史记录失败:', error);
        document.getElementById('historyList').innerHTML = `
            <div class="loading-placeholder">
                <i class="fas fa-exclamation-triangle"></i> 加载失败: ${error.message}
            </div>
        `;
        showToast('加载历史记录失败', 'error');
    }
}

/**
 * 显示历史记录列表
 */
function displayHistoryList(records) {
    const historyList = document.getElementById('historyList');

    if (records.length === 0) {
        historyList.innerHTML = `
            <div class="loading-placeholder">
                <i class="fas fa-inbox"></i> 暂无历史记录
            </div>
        `;
        return;
    }

    const html = records.map(record => `
        <div class="history-item">
            <div class="history-item-header">
                <div class="history-item-title">
                    <i class="fas fa-user"></i> ${escapeHtml(record.character_name)}
                </div>
                <div class="history-item-date">
                    <i class="fas fa-clock"></i> ${record.created_at_formatted}
                </div>
            </div>
            <div class="history-item-description">
                ${record.additional_description ? escapeHtml(record.additional_description) : '无补充描述'}
            </div>
            <div class="history-item-actions">
                <button class="btn-view" onclick="viewHistoryDetail('${record.id}')">
                    <i class="fas fa-eye"></i> 查看详情
                </button>
                <button class="btn-delete" onclick="deleteHistoryRecord('${record.id}')">
                    <i class="fas fa-trash"></i> 删除
                </button>
            </div>
        </div>
    `).join('');

    historyList.innerHTML = html;
}

/**
 * 更新分页控件
 */
function updatePagination(pagination) {
    const paginationDiv = document.getElementById('historyPagination');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const pageInfo = document.getElementById('pageInfo');

    // 计算总页数
    const totalPages = Math.ceil(pagination.total / pagination.limit);

    // 更新页面信息
    pageInfo.textContent = `第 ${pagination.page} 页，共 ${totalPages} 页`;

    // 更新按钮状态
    prevBtn.disabled = pagination.page <= 1;
    nextBtn.disabled = pagination.page >= totalPages;

    // 显示分页控件
    if (totalPages > 1) {
        paginationDiv.style.display = 'flex';
    } else {
        paginationDiv.style.display = 'none';
    }
}

/**
 * 搜索历史记录
 */
function searchHistory() {
    const searchInput = document.getElementById('searchInput');
    searchQuery = searchInput.value.trim();
    currentPage = 1;
    loadHistory(1);
}

/**
 * 查看历史记录详情
 */
async function viewHistoryDetail(recordId) {
    try {
        // 显示模态框
        const modal = document.getElementById('historyModal');
        const modalBody = document.getElementById('historyModalBody');

        modal.style.display = 'flex';
        modalBody.innerHTML = `
            <div class="loading-placeholder">
                <i class="fas fa-spinner fa-spin"></i> 加载中...
            </div>
        `;

        // 请求详情数据
        const response = await fetch(`/api/history/${recordId}`);
        const data = await response.json();

        if (data.success) {
            currentHistoryRecord = data.data;
            displayHistoryDetail(data.data);
        } else {
            throw new Error(data.error || '加载失败');
        }

    } catch (error) {
        console.error('加载记录详情失败:', error);
        document.getElementById('historyModalBody').innerHTML = `
            <div class="loading-placeholder">
                <i class="fas fa-exclamation-triangle"></i> 加载失败: ${error.message}
            </div>
        `;
        showToast('加载记录详情失败', 'error');
    }
}

/**
 * 显示历史记录详情
 */
function displayHistoryDetail(record) {
    const modalBody = document.getElementById('historyModalBody');

    const html = `
        <div class="history-detail">
            <div class="detail-section">
                <h4><i class="fas fa-info-circle"></i> 基本信息</h4>
                <div class="detail-grid">
                    <div class="detail-item">
                        <label>角色名称:</label>
                        <span>${escapeHtml(record.character_name)}</span>
                    </div>
                    <div class="detail-item">
                        <label>分析时间:</label>
                        <span>${record.created_at_formatted}</span>
                    </div>
                    <div class="detail-item">
                        <label>文件名:</label>
                        <span>${escapeHtml(record.image_filename)}</span>
                    </div>
                    <div class="detail-item">
                        <label>文件大小:</label>
                        <span>${formatFileSize(record.file_size)}</span>
                    </div>
                    <div class="detail-item">
                        <label>分析耗时:</label>
                        <span>${record.analysis_duration ? record.analysis_duration.toFixed(2) + '秒' : '未知'}</span>
                    </div>
                </div>
                ${record.additional_description ? `
                    <div class="detail-item full-width">
                        <label>补充描述:</label>
                        <div class="description-text">${escapeHtml(record.additional_description)}</div>
                    </div>
                ` : ''}
            </div>

            <div class="detail-section">
                <h4><i class="fas fa-image"></i> 图片分析结果</h4>
                <div class="analysis-text">${formatAnalysisText(record.image_analysis)}</div>
            </div>

            <div class="detail-section">
                <h4><i class="fas fa-magic"></i> 综合分析结果</h4>
                <div class="analysis-text">${formatAnalysisText(record.comprehensive_analysis)}</div>
            </div>
        </div>
    `;

    modalBody.innerHTML = html;
}

/**
 * 删除历史记录
 */
async function deleteHistoryRecord(recordId) {
    if (!confirm('确定要删除这条记录吗？此操作不可恢复。')) {
        return;
    }

    try {
        const response = await fetch(`/api/history/${recordId}`, {
            method: 'DELETE'
        });

        const data = await response.json();

        if (data.success) {
            showToast('记录删除成功', 'success');
            loadHistory(currentPage); // 重新加载当前页
        } else {
            throw new Error(data.error || '删除失败');
        }

    } catch (error) {
        console.error('删除记录失败:', error);
        showToast('删除记录失败: ' + error.message, 'error');
    }
}

/**
 * 关闭历史记录模态框
 */
function closeHistoryModal() {
    const modal = document.getElementById('historyModal');
    modal.style.display = 'none';
    currentHistoryRecord = null;
}

/**
 * 下载历史记录结果
 */
function downloadHistoryResult() {
    if (!currentHistoryRecord) {
        showToast('没有可下载的记录', 'error');
        return;
    }

    // 构建下载数据
    const resultData = {
        timestamp: currentHistoryRecord.created_at,
        character_name: currentHistoryRecord.character_name,
        additional_description: currentHistoryRecord.additional_description || '',
        image_analysis: currentHistoryRecord.image_analysis,
        comprehensive_analysis: currentHistoryRecord.comprehensive_analysis
    };

    // 调用现有的下载函数
    downloadResultData(resultData);
}

// ==================== 统计信息功能 ====================

/**
 * 加载统计信息
 */
async function loadStatistics() {
    try {
        const response = await fetch('/api/statistics');
        const data = await response.json();

        if (data.success) {
            displayStatistics(data.data);
        } else {
            throw new Error(data.error || '加载失败');
        }

    } catch (error) {
        console.error('加载统计信息失败:', error);
        showToast('加载统计信息失败', 'error');
    }
}

/**
 * 显示统计信息
 */
function displayStatistics(stats) {
    // 更新统计卡片
    document.getElementById('totalCount').textContent = stats.total_count || 0;
    document.getElementById('todayCount').textContent = stats.today_count || 0;
    document.getElementById('weekCount').textContent = stats.week_count || 0;

    // 更新热门角色
    const popularCharacters = document.getElementById('popularCharacters');

    if (stats.popular_characters && stats.popular_characters.length > 0) {
        const html = stats.popular_characters.map(character => `
            <div class="character-item">
                <span class="character-name">${escapeHtml(character.name)}</span>
                <span class="character-count">${character.count}</span>
            </div>
        `).join('');

        popularCharacters.innerHTML = html;
    } else {
        popularCharacters.innerHTML = `
            <div class="loading-placeholder">
                <i class="fas fa-inbox"></i> 暂无数据
            </div>
        `;
    }
}

// ==================== 工具函数 ====================

/**
 * HTML转义
 */
function escapeHtml(text) {
    if (!text) return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * 格式化文件大小
 */
function formatFileSize(bytes) {
    if (!bytes) return '未知';

    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
}

/**
 * 格式化分析文本
 */
function formatAnalysisText(text) {
    if (!text) return '无分析结果';

    // 将换行符转换为HTML换行
    return escapeHtml(text).replace(/\n/g, '<br>');
}

/**
 * 下载结果数据（支持历史记录）
 */
function downloadResultData(resultData) {
    fetch('/download_result', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            result: resultData
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 创建下载链接
            const link = document.createElement('a');
            link.href = data.download_url;
            link.download = '';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showToast('开始下载结果文件', 'success');
        } else {
            throw new Error(data.error || '下载失败');
        }
    })
    .catch(error => {
        console.error('下载失败:', error);
        showToast('下载失败: ' + error.message, 'error');
    });
}

// ==================== 页面初始化增强 ====================

/**
 * 初始化历史记录功能
 */
function initializeHistoryFeatures() {
    // 添加搜索框回车事件
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchHistory();
            }
        });
    }

    // 添加模态框点击外部关闭事件
    const modal = document.getElementById('historyModal');
    if (modal) {
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeHistoryModal();
            }
        });
    }
}
