/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 30px;
}

/* 导航菜单 */
.nav-menu {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;
}

.nav-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 12px 24px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.nav-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

.nav-btn.active {
    background: rgba(255, 255, 255, 0.9);
    color: #667eea;
    border-color: white;
}

.nav-btn.active:hover {
    background: white;
    color: #5a6fd8;
}

/* 主要内容区域 */
.main-content {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

/* 上传区域 */
.upload-section {
    margin-bottom: 30px;
}

.upload-area {
    border: 3px dashed #667eea;
    border-radius: 15px;
    padding: 40px;
    text-align: center;
    background: #f8f9ff;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: #764ba2;
    background: #f0f2ff;
}

.upload-area.dragover {
    border-color: #764ba2;
    background: #e8ebff;
    transform: scale(1.02);
}

.upload-icon {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 20px;
}

.upload-content h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: #333;
}

.upload-content p {
    color: #666;
    margin-bottom: 10px;
}

.file-info {
    font-size: 0.9rem;
    color: #999;
}

.upload-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-size: 1rem;
    cursor: pointer;
    margin-top: 20px;
    transition: all 0.3s ease;
}

.upload-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* 图片预览 */
.image-preview {
    position: relative;
    margin-top: 20px;
    text-align: center;
}

.image-preview img {
    max-width: 100%;
    max-height: 300px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.remove-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #ff4757;
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    cursor: pointer;
    font-size: 0.9rem;
}

/* 表单样式 */
.form-section {
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
}

.analyze-btn {
    width: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 15px;
    border-radius: 10px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.analyze-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.analyze-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

/* 加载状态 */
.loading-section {
    text-align: center;
    padding: 40px;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.progress-steps {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-top: 30px;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    opacity: 0.5;
    transition: opacity 0.3s ease;
}

.step.active {
    opacity: 1;
    color: #667eea;
}

.step i {
    font-size: 1.5rem;
}

/* 结果展示 */
.results-section {
    margin-top: 30px;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.download-btn,
.new-analysis-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.download-btn:hover,
.new-analysis-btn:hover {
    background: #218838;
    transform: translateY(-1px);
}

.result-card {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    border-left: 4px solid #667eea;
}

.result-card h3 {
    margin-bottom: 15px;
    color: #333;
}

.result-text {
    line-height: 1.6;
    white-space: pre-wrap;
    color: #555;
}

.results-actions {
    text-align: center;
    margin-top: 30px;
}

/* 页脚 */
.footer {
    text-align: center;
    color: white;
    opacity: 0.8;
    padding: 20px;
}

/* 消息提示 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #333;
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    z-index: 1000;
}

.toast.show {
    transform: translateX(0);
}

.toast.error {
    background: #dc3545;
}

.toast.success {
    background: #28a745;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .main-content {
        padding: 20px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .progress-steps {
        flex-direction: column;
        gap: 15px;
    }
    
    .results-header {
        flex-direction: column;
        gap: 15px;
    }
}

/* 历史记录和统计信息区域 */
.history-section,
.stats-section {
    margin-top: 30px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #f0f0f0;
}

.section-header h2 {
    color: #333;
    margin: 0;
    font-size: 1.5rem;
}

.refresh-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.refresh-btn:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.loading-placeholder {
    text-align: center;
    padding: 40px;
    color: #666;
    font-size: 1.1rem;
}

/* 统计信息网格 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.stat-card {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 15px;
    padding: 25px;
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
    border: 1px solid #dee2e6;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.stat-icon {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.stat-info h3 {
    margin: 0 0 5px 0;
    font-size: 2rem;
    color: #333;
    font-weight: bold;
}

.stat-info p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

/* 历史记录列表 */
.history-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.history-item {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    border-left: 4px solid #667eea;
    transition: all 0.3s ease;
}

.history-item:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.history-character {
    font-weight: bold;
    color: #333;
    font-size: 1.1rem;
}

.history-date {
    color: #666;
    font-size: 0.9rem;
}

.history-description {
    color: #555;
    margin-bottom: 10px;
    line-height: 1.4;
}

.history-actions {
    display: flex;
    gap: 10px;
}

.history-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 15px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.history-btn:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.history-btn.delete {
    background: #dc3545;
}

.history-btn.delete:hover {
    background: #c82333;
}

/* 页脚 */
.footer {
    text-align: center;
    padding: 20px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}
