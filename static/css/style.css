/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* 导航标签样式 */
.nav-tabs {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;
}

.nav-tab {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
    font-weight: 500;
}

.nav-tab:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.nav-tab.active {
    background: white;
    color: #667eea;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.nav-tab i {
    margin-right: 8px;
}

/* 标签页内容 */
.tab-content {
    display: block;
}

/* 主要内容区域 */
.main-content {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

/* 上传区域 */
.upload-section {
    margin-bottom: 30px;
}

.upload-area {
    border: 3px dashed #667eea;
    border-radius: 15px;
    padding: 40px;
    text-align: center;
    background: #f8f9ff;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: #764ba2;
    background: #f0f2ff;
}

.upload-area.dragover {
    border-color: #764ba2;
    background: #e8ebff;
    transform: scale(1.02);
}

.upload-icon {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 20px;
}

.upload-content h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: #333;
}

.upload-content p {
    color: #666;
    margin-bottom: 10px;
}

.file-info {
    font-size: 0.9rem;
    color: #999;
}

.upload-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-size: 1rem;
    cursor: pointer;
    margin-top: 20px;
    transition: all 0.3s ease;
}

.upload-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* 图片预览 */
.image-preview {
    position: relative;
    margin-top: 20px;
    text-align: center;
}

.image-preview img {
    max-width: 100%;
    max-height: 300px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.remove-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #ff4757;
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    cursor: pointer;
    font-size: 0.9rem;
}

/* 表单样式 */
.form-section {
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
}

.analyze-btn {
    width: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 15px;
    border-radius: 10px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.analyze-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.analyze-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

/* 加载状态 */
.loading-section {
    text-align: center;
    padding: 40px;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.progress-steps {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-top: 30px;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    opacity: 0.5;
    transition: opacity 0.3s ease;
}

.step.active {
    opacity: 1;
    color: #667eea;
}

.step i {
    font-size: 1.5rem;
}

/* 结果展示 */
.results-section {
    margin-top: 30px;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.download-btn,
.new-analysis-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.download-btn:hover,
.new-analysis-btn:hover {
    background: #218838;
    transform: translateY(-1px);
}

.result-card {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    border-left: 4px solid #667eea;
}

.result-card h3 {
    margin-bottom: 15px;
    color: #333;
}

.result-text {
    line-height: 1.6;
    white-space: pre-wrap;
    color: #555;
}

.results-actions {
    text-align: center;
    margin-top: 30px;
}

/* 页脚 */
.footer {
    text-align: center;
    color: white;
    opacity: 0.8;
    padding: 20px;
}

/* 消息提示 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #333;
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    z-index: 1000;
}

.toast.show {
    transform: translateX(0);
}

.toast.error {
    background: #dc3545;
}

.toast.success {
    background: #28a745;
}

/* 历史记录样式 */
.history-section {
    padding: 20px 0;
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;
}

.history-header h2 {
    color: #333;
    font-size: 1.8rem;
}

.history-controls {
    display: flex;
    gap: 15px;
    align-items: center;
}

.search-box {
    display: flex;
    background: #f8f9fa;
    border-radius: 25px;
    overflow: hidden;
    border: 2px solid #e9ecef;
}

.search-box input {
    border: none;
    padding: 10px 15px;
    background: transparent;
    outline: none;
    width: 200px;
}

.search-box button {
    background: #667eea;
    color: white;
    border: none;
    padding: 10px 15px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.search-box button:hover {
    background: #5a6fd8;
}

.refresh-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.refresh-btn:hover {
    background: #218838;
    transform: translateY(-2px);
}

.history-list {
    min-height: 400px;
}

.history-item {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 15px;
    border-left: 4px solid #667eea;
    transition: all 0.3s ease;
}

.history-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.history-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.history-item-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
}

.history-item-date {
    color: #666;
    font-size: 0.9rem;
}

.history-item-description {
    color: #666;
    margin-bottom: 15px;
    line-height: 1.5;
}

.history-item-actions {
    display: flex;
    gap: 10px;
}

.btn-view, .btn-delete {
    padding: 8px 16px;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.btn-view {
    background: #667eea;
    color: white;
}

.btn-view:hover {
    background: #5a6fd8;
}

.btn-delete {
    background: #dc3545;
    color: white;
}

.btn-delete:hover {
    background: #c82333;
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-top: 30px;
}

.page-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.page-btn:hover:not(:disabled) {
    background: #5a6fd8;
    transform: translateY(-2px);
}

.page-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.page-info {
    font-weight: 600;
    color: #333;
}

/* 统计信息样式 */
.statistics-section {
    padding: 20px 0;
}

.statistics-section h2 {
    color: #333;
    font-size: 1.8rem;
    margin-bottom: 30px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    gap: 20px;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.stat-content h3 {
    font-size: 2.5rem;
    margin-bottom: 5px;
}

.stat-content p {
    font-size: 1rem;
    opacity: 0.9;
}

.popular-characters {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 15px;
}

.popular-characters h3 {
    color: #333;
    margin-bottom: 20px;
    font-size: 1.4rem;
}

.character-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
}

.character-item {
    background: white;
    padding: 15px;
    border-radius: 10px;
    border-left: 4px solid #667eea;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.character-name {
    font-weight: 600;
    color: #333;
}

.character-count {
    background: #667eea;
    color: white;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.9rem;
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 15px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-header {
    background: #667eea;
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.4rem;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.modal-body {
    padding: 30px;
    overflow-y: auto;
    flex: 1;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.btn-primary, .btn-secondary {
    padding: 10px 20px;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.loading-placeholder {
    text-align: center;
    padding: 40px;
    color: #666;
    font-size: 1.1rem;
}

.loading-placeholder i {
    margin-right: 10px;
}

/* 历史记录详情样式 */
.history-detail {
    max-height: 60vh;
    overflow-y: auto;
}

.detail-section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.detail-section:last-child {
    border-bottom: none;
}

.detail-section h4 {
    color: #333;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.detail-item.full-width {
    grid-column: 1 / -1;
}

.detail-item label {
    font-weight: 600;
    color: #666;
    font-size: 0.9rem;
}

.detail-item span {
    color: #333;
    font-size: 1rem;
}

.description-text {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 8px;
    color: #333;
    line-height: 1.5;
}

.analysis-text {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    color: #333;
    line-height: 1.6;
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .main-content {
        padding: 20px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .progress-steps {
        flex-direction: column;
        gap: 15px;
    }
    
    .results-header {
        flex-direction: column;
        gap: 15px;
    }
}
