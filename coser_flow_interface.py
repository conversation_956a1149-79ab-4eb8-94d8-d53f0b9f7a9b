#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Coser分析工作流用户界面
提供简单易用的接口来运行CrewAI Flows
"""

import os
import sys
from coser_analysis_flow import run_coser_analysis

def main():
    """主界面函数"""
    print("🎭 Coser分析工作流 - CrewAI Flows版本")
    print("=" * 60)
    print("📋 功能说明:")
    print("• 步骤一: 图片分析Agent分析Coser图片信息")
    print("• 步骤二: 综合分析Agent生成角色信息和文生图prompt")
    print("=" * 60)
    
    while True:
        print("\n💬 请选择操作:")
        print("1. 分析Coser图片")
        print("2. 使用默认测试图片")
        print("3. 退出")
        
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == "1":
            # 用户自定义分析
            image_path = input("📸 请输入图片路径: ").strip()
            if not os.path.exists(image_path):
                print("❌ 图片文件不存在，请检查路径")
                continue
                
            character_name = input("🎭 请输入角色名称: ").strip()
            additional_description = input("📝 请输入补充描述 (可选): ").strip()
            
            print("\n🚀 开始分析...")
            result = run_coser_analysis(image_path, character_name, additional_description)
            
            if result:
                print("\n" + "=" * 60)
                print("📊 分析结果:")
                print("=" * 60)
                print("\n🖼️  图片分析结果:")
                print("-" * 40)
                print(result["image_analysis"])
                print("\n🔍 综合分析结果:")
                print("-" * 40)
                print(result["comprehensive_analysis"])
                
                # 询问是否保存结果
                save_choice = input("\n💾 是否保存分析结果到文件? (y/n): ").strip().lower()
                if save_choice == 'y':
                    save_results_to_file(result, character_name)
            else:
                print("❌ 分析失败，请检查配置和网络连接")
                
        elif choice == "2":
            # 使用默认测试图片
            print("\n🚀 使用默认测试图片进行分析...")
            result = run_coser_analysis(
                image_path="static/WechatIMG8_640xNaN.jpg",
                character_name="蓝发角色",
                additional_description="神秘的蓝发角色，具有战斗风格的服装"
            )
            
            if result:
                print("\n" + "=" * 60)
                print("📊 默认测试分析结果:")
                print("=" * 60)
                print("\n🖼️  图片分析结果:")
                print("-" * 40)
                print(result["image_analysis"])
                print("\n🔍 综合分析结果:")
                print("-" * 40)
                print(result["comprehensive_analysis"])
            else:
                print("❌ 分析失败，请检查配置和网络连接")
                
        elif choice == "3":
            print("\n👋 感谢使用Coser分析工作流！")
            break
            
        else:
            print("❌ 无效选择，请输入1-3")

def save_results_to_file(result, character_name):
    """保存分析结果到文件"""
    try:
        # 创建输出目录
        output_dir = "output"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 生成文件名
        safe_name = "".join(c for c in character_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        filename = f"{output_dir}/coser_analysis_{safe_name}.md"
        
        # 写入文件
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"# Coser分析报告 - {character_name}\n\n")
            f.write("## 图片分析结果\n\n")
            f.write(result["image_analysis"])
            f.write("\n\n## 综合分析结果\n\n")
            f.write(result["comprehensive_analysis"])
            f.write(f"\n\n---\n*生成时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*\n")
        
        print(f"✅ 分析结果已保存到: {filename}")
        
    except Exception as e:
        print(f"❌ 保存文件失败: {str(e)}")

def show_help():
    """显示帮助信息"""
    print("\n📖 使用说明:")
    print("=" * 60)
    print("1. 确保已配置.env文件中的ZHIPUAI_API_KEY")
    print("2. 图片格式支持: jpg, jpeg, png")
    print("3. 建议图片大小不超过10MB")
    print("4. 角色名称和描述将影响分析质量")
    print("5. 分析结果可保存为Markdown格式")
    print("=" * 60)

if __name__ == "__main__":
    # 检查环境配置
    if not os.getenv("ZHIPUAI_API_KEY"):
        print("❌ 错误: 未找到ZHIPUAI_API_KEY环境变量")
        print("请在.env文件中设置您的智谱AI API密钥")
        sys.exit(1)
    
    # 检查测试图片
    if not os.path.exists("static/WechatIMG8_640xNaN.jpg"):
        print("⚠️  警告: 未找到默认测试图片")
        print("请确保static/WechatIMG8_640xNaN.jpg文件存在")
    
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
        print("请检查配置和网络连接")
