# 🎭 Coser分析工作流 Web应用使用说明

## 🚀 启动应用

```bash
# 进入项目目录
cd /app/toq-crawai

# 启动Web应用
python app.py
```

启动成功后，访问：**http://localhost:8888**

## 📱 功能介绍

### 1. 文件上传
- **支持格式**：PNG, JPG, JPEG, GIF, WEBP
- **文件大小**：最大16MB
- **上传方式**：
  - 点击"选择文件"按钮
  - 直接拖拽图片到上传区域

### 2. 信息输入
- **角色名称**：必填项，如"利姆露"
- **补充描述**：可选项，如"森林场景，魔法粒子效果"

### 3. 分析过程
- **步骤一**：图片分析（GLM-4V模型）
- **步骤二**：综合分析（GLM-Z1-Air模型）
- **步骤三**：生成结果

### 4. 结果展示
- **图片分析结果**：六维度专业分析
- **综合分析结果**：角色信息 + 文生图prompt

### 5. 结果下载
- 点击"下载结果"按钮
- 自动生成TXT格式的分析报告

## 🎯 使用流程

1. **上传图片** → 拖拽或选择Coser图片
2. **填写信息** → 输入角色名称和补充描述
3. **开始分析** → 点击"开始分析"按钮
4. **等待结果** → 观看分析进度
5. **查看结果** → 浏览详细分析报告
6. **下载保存** → 下载分析结果文件

## 🔧 技术架构

### 后端技术
- **框架**：Flask
- **AI模型**：GLM-4V + GLM-Z1-Air
- **工作流**：CrewAI Flows

### 前端技术
- **HTML5**：语义化结构
- **CSS3**：响应式设计 + 渐变效果
- **JavaScript**：异步交互 + 文件上传

### 核心功能
- **文件上传**：支持拖拽上传和预览
- **实时验证**：表单验证和错误提示
- **进度指示**：分析步骤可视化
- **结果展示**：美观的卡片式布局
- **文件下载**：一键下载分析报告

## 📊 API接口

### 1. 文件上传
```
POST /upload
Content-Type: multipart/form-data
```

### 2. 图片分析
```
POST /analyze
Content-Type: application/json
{
  "filepath": "static/uploads/xxx.jpg",
  "character_name": "利姆露",
  "additional_description": "森林场景"
}
```

### 3. 结果下载
```
POST /download_result
Content-Type: application/json
{
  "result": { ... }
}
```

## 🎨 界面特色

### 视觉设计
- **渐变背景**：紫色渐变营造科技感
- **卡片布局**：清晰的信息层次
- **图标系统**：FontAwesome图标库
- **响应式**：适配各种屏幕尺寸

### 交互体验
- **拖拽上传**：直观的文件上传方式
- **实时反馈**：即时的状态提示
- **进度指示**：清晰的分析步骤
- **平滑动画**：优雅的过渡效果

## 🛡️ 安全特性

- **文件类型验证**：只允许图片格式
- **文件大小限制**：防止过大文件上传
- **路径安全**：使用secure_filename处理文件名
- **错误处理**：完善的异常捕获机制

## 📁 项目结构

```
/app/toq-crawai/
├── app.py                          # Flask后端应用
├── templates/
│   └── index.html                  # 主页面
├── static/
│   ├── css/
│   │   └── style.css              # 样式文件
│   ├── js/
│   │   └── main.js                # JavaScript功能
│   └── uploads/                   # 上传文件目录
├── coser_analysis_flow.py         # CrewAI Flows工作流
└── Web应用使用说明.md             # 本说明文档
```

## 🎉 优势特点

1. **用户友好**：无需命令行操作，图形化界面
2. **功能完整**：集成完整的分析工作流
3. **响应式设计**：支持PC和移动设备
4. **实时反馈**：清晰的状态提示和进度指示
5. **结果保存**：支持分析结果下载
6. **现代化UI**：美观的界面设计

现在您可以通过Web浏览器轻松使用Coser分析功能，无需任何命令行操作！🎭✨
