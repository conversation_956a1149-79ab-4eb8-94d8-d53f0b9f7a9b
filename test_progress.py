#!/usr/bin/env python3
"""
测试前端进度获取功能的脚本
"""
import requests
import json
import time

def test_progress_api():
    """测试进度API"""
    base_url = "http://localhost:8888"
    
    # 1. 启动一个分析任务
    print("🚀 启动分析任务...")
    analyze_data = {
        "filepath": "static/uploads/16ceb31d90f4449d8deef60e01380627.png",
        "character_name": "测试角色",
        "additional_description": "测试描述"
    }
    
    try:
        response = requests.post(
            f"{base_url}/analyze",
            headers={"Content-Type": "application/json"},
            json=analyze_data
        )
        
        if response.status_code != 200:
            print(f"❌ 启动分析失败: HTTP {response.status_code}")
            print(f"响应内容: {response.text}")
            return
            
        result = response.json()
        if not result.get('success'):
            print(f"❌ 启动分析失败: {result.get('error', '未知错误')}")
            return
            
        task_id = result['task_id']
        print(f"✅ 分析任务启动成功，任务ID: {task_id}")
        
        # 2. 轮询进度
        print("🔄 开始轮询进度...")
        poll_count = 0
        max_polls = 60  # 最多轮询60次（60秒）
        
        while poll_count < max_polls:
            poll_count += 1
            print(f"\n📊 第 {poll_count} 次轮询...")
            
            try:
                progress_response = requests.get(f"{base_url}/progress/{task_id}")
                
                if progress_response.status_code == 404:
                    print("❌ 任务不存在")
                    break
                elif progress_response.status_code != 200:
                    print(f"❌ 获取进度失败: HTTP {progress_response.status_code}")
                    print(f"响应内容: {progress_response.text}")
                    break
                
                progress_data = progress_response.json()
                print(f"📈 进度: {progress_data.get('progress', 0)}%")
                print(f"🔧 步骤: {progress_data.get('step', 'N/A')} - {progress_data.get('step_name', 'N/A')}")
                print(f"📊 状态: {progress_data.get('status', 'N/A')}")
                
                if progress_data.get('error'):
                    print(f"❌ 分析出错: {progress_data['error']}")
                    break
                
                if progress_data.get('status') == 'completed':
                    print("✅ 分析完成！")
                    if progress_data.get('result'):
                        print("📋 获取到分析结果")
                    else:
                        print("⚠️ 分析完成但未获取到结果")
                    break
                elif progress_data.get('status') == 'error':
                    print(f"❌ 分析失败: {progress_data.get('error', '未知错误')}")
                    break
                
            except requests.exceptions.RequestException as e:
                print(f"❌ 网络请求错误: {e}")
                break
            
            time.sleep(1)  # 等待1秒
        
        if poll_count >= max_polls:
            print("⏰ 轮询超时")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求错误: {e}")
    except Exception as e:
        print(f"❌ 未知错误: {e}")

if __name__ == "__main__":
    test_progress_api()
