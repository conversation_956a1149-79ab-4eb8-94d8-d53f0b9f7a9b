<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎭 Coser分析工作流</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <h1><i class="fas fa-magic"></i> Coser分析工作流</h1>
            <p class="subtitle">基于CrewAI Flows的专业Coser图片分析系统</p>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 上传区域 -->
            <section class="upload-section">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-content">
                        <i class="fas fa-cloud-upload-alt upload-icon"></i>
                        <h3>上传Coser图片</h3>
                        <p>拖拽图片到此处或点击选择文件</p>
                        <p class="file-info">支持 PNG, JPG, JPEG, GIF, WEBP 格式，最大16MB</p>
                        <input type="file" id="fileInput" accept="image/*" style="display: none;">
                        <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                            <i class="fas fa-folder-open"></i> 选择文件
                        </button>
                    </div>
                </div>
                
                <!-- 图片预览 -->
                <div class="image-preview" id="imagePreview" style="display: none;">
                    <img id="previewImg" src="" alt="预览图片">
                    <button class="remove-btn" onclick="removeImage()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </section>

            <!-- 输入表单 -->
            <section class="form-section">
                <div class="form-group">
                    <label for="characterName">
                        <i class="fas fa-user"></i> 角色名称 *
                    </label>
                    <input type="text" id="characterName" placeholder="请输入角色名称，如：利姆露" required>
                </div>
                
                <div class="form-group">
                    <label for="additionalDescription">
                        <i class="fas fa-edit"></i> 补充描述
                    </label>
                    <textarea id="additionalDescription" placeholder="请输入补充描述，如：森林场景，魔法粒子效果" rows="3"></textarea>
                </div>
                
                <button class="analyze-btn" id="analyzeBtn" onclick="startAnalysis()" disabled>
                    <i class="fas fa-search"></i> 开始分析
                </button>
            </section>

            <!-- 加载状态 -->
            <section class="loading-section" id="loadingSection" style="display: none;">
                <div class="loading-content">
                    <div class="spinner"></div>
                    <h3>正在分析中...</h3>
                    <p id="loadingText">请稍候，AI正在分析您的图片</p>
                    <div class="progress-steps">
                        <div class="step" id="step1">
                            <i class="fas fa-eye"></i>
                            <span>图片分析</span>
                        </div>
                        <div class="step" id="step2">
                            <i class="fas fa-brain"></i>
                            <span>综合分析</span>
                        </div>
                        <div class="step" id="step3">
                            <i class="fas fa-check"></i>
                            <span>生成结果</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 结果展示 -->
            <section class="results-section" id="resultsSection" style="display: none;">
                <div class="results-header">
                    <h2><i class="fas fa-chart-line"></i> 分析结果</h2>
                    <button class="download-btn" onclick="downloadResult()">
                        <i class="fas fa-download"></i> 下载结果
                    </button>
                </div>
                
                <div class="results-content">
                    <!-- 图片分析结果 -->
                    <div class="result-card">
                        <h3><i class="fas fa-image"></i> 图片分析结果</h3>
                        <div class="result-text" id="imageAnalysisResult"></div>
                    </div>
                    
                    <!-- 综合分析结果 -->
                    <div class="result-card">
                        <h3><i class="fas fa-magic"></i> 综合分析结果</h3>
                        <div class="result-text" id="comprehensiveAnalysisResult"></div>
                    </div>
                </div>
                
                <div class="results-actions">
                    <button class="new-analysis-btn" onclick="newAnalysis()">
                        <i class="fas fa-plus"></i> 新的分析
                    </button>
                </div>
            </section>

            <!-- 历史记录区域 -->
            <section class="history-section" id="historySection" style="display: none;">
                <div class="section-header">
                    <h2><i class="fas fa-history"></i> 分析历史</h2>
                    <button class="refresh-btn" onclick="loadHistory()">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                </div>
                <div class="history-content" id="historyContent">
                    <div class="loading-placeholder">
                        <i class="fas fa-spinner fa-spin"></i> 加载历史记录中...
                    </div>
                </div>
            </section>

            <!-- 统计信息区域 -->
            <section class="stats-section" id="statsSection" style="display: none;">
                <div class="section-header">
                    <h2><i class="fas fa-chart-bar"></i> 统计信息</h2>
                    <button class="refresh-btn" onclick="loadStats()">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                </div>
                <div class="stats-content">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-number" id="totalAnalyses">-</div>
                                <div class="stat-label">总分析次数</div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-number" id="avgDuration">-</div>
                                <div class="stat-label">平均分析时长</div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-calendar-day"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-number" id="todayAnalyses">-</div>
                                <div class="stat-label">今日分析</div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-number" id="popularCharacter">-</div>
                                <div class="stat-label">热门角色</div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- 页脚 -->
        <footer class="footer">
            <p>© 2024 Coser分析工作流 | 基于CrewAI Flows + GLM模型</p>
        </footer>
    </div>

    <!-- 消息提示 -->
    <div class="toast" id="toast"></div>

    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>
